@extends('layouts.admin')

@section('title', 'User Management - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">User Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">Admin</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Users</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Users Table Card -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-list me-2"></i>Users
                    </div>
                    <div class="d-flex">
                        <div class="me-2">
                            <span class="badge bg-primary-transparent">Total: {{ $users->total() }} users</span>
                        </div>

                        @php
                            $roleCounts = $users->getCollection()->groupBy('role')->map->count();
                            $roleBadgeMap = [
                                'admin' => 'danger',
                                'member' => 'info',
                                'user' => 'secondary',
                            ];
                        @endphp

                        @foreach ($roleCounts as $role => $count)
                            @php
                                $badgeColor = $roleBadgeMap[$role] ?? 'secondary';
                                $roleLabel = $role ? ucfirst($role) : 'Unknown';
                            @endphp
                            <div class="me-2">
                                <span class="badge bg-{{ $badgeColor }}-transparent">{{ $roleLabel }}: {{ $count }}</span>
                            </div>
                        @endforeach

                        <div class="ms-3">
                            <a href="{{ route('admin.users.create') }}" class="btn btn-primary btn-sm">
                                <i class="ti ti-plus me-1"></i>Add New User
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Users Table -->
                    <div class="table-responsive">
                        <table class="table text-nowrap table-hover" id="usersTable">
                            <thead>
                                <tr>
                                    <th scope="col">User</th>
                                    <th scope="col">Email</th>
                                    <th scope="col">Username</th>
                                    <th scope="col">Role</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($users as $user)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span class="avatar avatar-sm avatar-rounded me-2">
                                                    <img src="{{ asset('assets/images/faces/4.jpg') }}" alt="">
                                                </span>
                                                <div>
                                                    <div class="lh-1">
                                                        <span class="fw-semibold">{{ $user->name }}</span>
                                                    </div>
                                                    <div>
                                                        <small class="text-muted">Joined {{ $user->created_at->format('M d, Y') }}</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ $user->email }}</td>
                                        <td>{{ $user->username ?? 'N/A' }}</td>
                                        <td>
                                            @php
                                                $roleColors = [
                                                    'admin' => 'danger',
                                                    'member' => 'info',
                                                    'user' => 'secondary',
                                                ];
                                                $roleColor = $roleColors[$user->role] ?? 'secondary';
                                                $roleLabel = $user->role ? ucfirst($user->role) : 'Unknown';
                                            @endphp
                                            <span class="badge bg-{{ $roleColor }}-transparent text-{{ $roleColor }}">{{ $roleLabel }}</span>
                                        </td>
                                        <td>
                                            @if ($user->isLocked())
                                                <span class="badge bg-danger-transparent text-danger">Locked</span>
                                            @else
                                                <span class="badge bg-success-transparent text-success">Active</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.users.show', $user) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" data-bs-placement="top" title="View">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                                <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" data-bs-placement="top" title="Edit">
                                                    <i class="ti ti-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">No users found.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                        <!-- Pagination -->
                        <div class="booking-pagination">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="pagination-info">
                                    Showing {{ $users->firstItem() ?? 0 }} to {{ $users->lastItem() ?? 0 }} of {{ $users->total() }} users
                                </div>
                                <div class="admin-pagination">
                                    <x-admin-pagination :paginator="$users" />
                                </div>
                            </div>
                        </div>

                </div>

            </div>
        </div>
    </div>

    <!-- DataTables Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
@endsection
