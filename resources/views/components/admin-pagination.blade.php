@props(['paginator'])

@if ($paginator->hasPages())
    <nav aria-label="Page navigation">
        <ul class="pagination mb-0 flex-wrap admin-pagination-seamless">
            {{-- Previous Page Link --}}
            @if ($paginator->onFirstPage())
                <li class="page-item disabled" aria-disabled="true">
                    <span class="page-link">
                        <i class="bx bx-chevron-left"></i>
                    </span>
                </li>
            @else
                <li class="page-item">
                    <a class="page-link" href="{{ $paginator->previousPageUrl() }}" rel="prev">
                        <i class="bx bx-chevron-left"></i>
                    </a>
                </li>
            @endif

            {{-- Pagination Elements --}}
            @foreach ($paginator->elements() as $element)
                {{-- "Three Dots" Separator --}}
                @if (is_string($element))
                    <li class="page-item disabled" aria-disabled="true">
                        <span class="page-link">
                            <i class="bx bx-dots-horizontal-rounded"></i>
                        </span>
                    </li>
                @endif

                {{-- Array Of Links --}}
                @if (is_array($element))
                    @foreach ($element as $page => $url)
                        @if ($page == $paginator->currentPage())
                            <li class="page-item active" aria-current="page">
                                <span class="page-link">{{ $page }}</span>
                            </li>
                        @else
                            <li class="page-item">
                                <a class="page-link" href="{{ $url }}">{{ $page }}</a>
                            </li>
                        @endif
                    @endforeach
                @endif
            @endforeach

            {{-- Next Page Link --}}
            @if ($paginator->hasMorePages())
                <li class="page-item">
                    <a class="page-link" href="{{ $paginator->nextPageUrl() }}" rel="next">
                        <i class="bx bx-chevron-right"></i>
                    </a>
                </li>
            @else
                <li class="page-item disabled" aria-disabled="true">
                    <span class="page-link">
                        <i class="bx bx-chevron-right"></i>
                    </span>
                </li>
            @endif
        </ul>
    </nav>

    {{-- Seamless Pagination Styling --}}
    <style>
        .admin-pagination-seamless .page-item {
            margin: 0 !important;
        }
        
        .admin-pagination-seamless .page-item .page-link {
            border-radius: 0 !important;
            border-right: 0;
            min-width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.375rem 0.75rem;
            font-size: 0.8125rem;
        }
        
        .admin-pagination-seamless .page-item:first-child .page-link {
            border-top-left-radius: 0.375rem !important;
            border-bottom-left-radius: 0.375rem !important;
        }
        
        .admin-pagination-seamless .page-item:last-child .page-link {
            border-top-right-radius: 0.375rem !important;
            border-bottom-right-radius: 0.375rem !important;
            border-right: 1px solid var(--bs-border-color);
        }
        
        .admin-pagination-seamless .page-item.active .page-link {
            background-color: var(--bs-primary);
            border-color: var(--bs-primary);
            color: white;
            border-right: 1px solid var(--bs-primary);
        }
        
        .admin-pagination-seamless .page-item:hover .page-link:not(.active) {
            background-color: var(--bs-light);
            color: var(--bs-dark);
        }
        
        .admin-pagination-seamless .page-item.disabled .page-link {
            opacity: 0.6;
            pointer-events: none;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .admin-pagination-seamless .page-item .page-link {
                min-width: 2.25rem;
                height: 2.25rem;
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
            }
        }
        
        @media (max-width: 576px) {
            .admin-pagination-seamless .page-item .page-link {
                min-width: 2rem;
                height: 2rem;
                font-size: 0.7rem;
                padding: 0.25rem 0.4rem;
            }
        }
    </style>
@endif
